<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>NLP UI</title>
  <style>
    body { display: flex; font-family: sans-serif; padding: 20px; }
    #left, #right { width: 50%; padding: 10px; }
    .token { display: inline-block; padding: 4px 6px; border-radius: 4px; margin: 1px; }
    .query-token { background-color: #d0e7ff; }
    .response-token { background-color: #d2f8d2; }
  </style>
</head>
<body>
  <div id="left">
    <h1>NLP Query</h1>
    <input type="text" id="user_input" placeholder="Type your query" size="50">
    <button onclick="sendQuery()">Send</button>
    <hr>
    <div id="tokens_display"></div>
  </div>
  <div id="right">
    <h1>NLP Processing Info</h1>
    <div><b>Latency:</b> <span id="latency"></span> sec</div>
    <div><b>Tokens/sec:</b> <span id="tokens_per_sec"></span></div>
    <div><b>Top Tokens (Next Probabilities):</b>
      <div id="top_tokens"></div>
    </div>
  </div>
  <script>
    async function sendQuery() {
      const input = document.getElementById("user_input").value;
      const res = await fetch('/query', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ input })
      });
      const data = await res.json();

      // Show tokens
      const tDiv = document.getElementById("tokens_display");
      tDiv.innerHTML = "";
      data.tokens.forEach((tok, i) => {
        const el = document.createElement("span");
        el.className = "token response-token";
        el.innerText = tok;
        tDiv.appendChild(el);
      });

      // Show metrics
      document.getElementById("latency").innerText = data.latency;
      document.getElementById("tokens_per_sec").innerText = data.tokens_per_sec;

      // Show top tokens
      const topDiv = document.getElementById("top_tokens");
      topDiv.innerHTML = "";
      data.top_tokens.forEach(t => {
        const el = document.createElement("div");
        el.innerText = `${t.token}: ${t.prob}`;
        topDiv.appendChild(el);
      });
    }
  </script>
</body>
</html>
