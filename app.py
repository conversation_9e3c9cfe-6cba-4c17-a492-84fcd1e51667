from flask import Flask, render_template, request, jsonify
import time
from llama_cpp import <PERSON>lama
import numpy as np
import torch.nn.functional as F

app = Flask(__name__)

# ⚡ Initialize model (adjust path)
llm = Llama(
    model_path=".model/tinyllama-1.1b.gguf",
    n_ctx=2048,
    n_threads=4
)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/query', methods=['POST'])
def query():
    data = request.get_json()
    user_input = data.get("input", "")

    start_time = time.time()

    output = llm(
        user_input,
        max_tokens=100,
        echo=True,
        logits_all=True  # Ensure your build supports this!
    )

    end_time = time.time()
    latency = end_time - start_time

    tokens_generated = output["usage"]["completion_tokens"]
    tokens_per_sec = tokens_generated / latency if latency > 0 else 0

    # Get response text + tokens
    response_text = output["choices"][0]["text"]
    token_ids = output["token_ids"]
    tokens = [llm.tokenizer.decode([tid]) for tid in token_ids]

    # Get logits for last step's top tokens
    top_tokens_info = []
    if "logits" in output:
        logits = output["logits"][-1]
        probs = F.softmax(torch.tensor(logits), dim=-1)
        topk = torch.topk(probs, 5)
        for idx, prob in zip(topk.indices.tolist(), topk.values.tolist()):
            top_tokens_info.append({
                "token": llm.tokenizer.decode([idx]),
                "prob": f"{prob:.4f}"
            })

    return jsonify({
        "query": user_input,
        "tokens": tokens,
        "latency": f"{latency:.3f}",
        "tokens_per_sec": f"{tokens_per_sec:.2f}",
        "top_tokens": top_tokens_info
    })

if __name__ == '__main__':
    app.run(debug=True)
